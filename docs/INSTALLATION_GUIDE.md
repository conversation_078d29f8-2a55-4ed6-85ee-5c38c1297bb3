# LifeTimer 安装指南

欢迎使用 LifeTimer！本指南将帮助您在 macOS 设备上安装和设置 LifeTimer 应用。

## 📋 系统要求

### 最低要求
- **操作系统**: macOS 13.0 (Ventura) 或更高版本
- **处理器**: Intel 或 Apple Silicon (M1/M2/M3)
- **内存**: 4GB RAM
- **存储空间**: 至少 50MB 可用空间

### 推荐配置
- **操作系统**: macOS 14.0 (Sonoma) 或更高版本
- **内存**: 8GB RAM 或更多
- **存储空间**: 100MB 可用空间

## 📦 下载应用

### 下载选项

您可以选择以下任一格式下载 LifeTimer：

1. **DMG 安装包** (推荐)
   - 文件名: `LifeTimer-1.0.dmg`
   - 适合大多数用户
   - 包含拖拽安装界面

2. **ZIP 压缩包**
   - 文件名: `LifeTimer-1.0.zip`
   - 适合高级用户
   - 需要手动移动到应用程序文件夹

## 🚀 安装步骤

### 方式一：使用 DMG 安装包（推荐）

1. **下载 DMG 文件**
   - 下载 `LifeTimer-1.0.dmg` 文件
   - 等待下载完成

2. **打开 DMG 文件**
   - 双击下载的 DMG 文件
   - 系统会自动挂载磁盘映像

3. **安装应用**
   - 在打开的窗口中，将 `LifeTimer.app` 拖拽到 `Applications` 文件夹
   - 等待复制完成

4. **弹出磁盘映像**
   - 在 Finder 侧边栏中，点击 LifeTimer 磁盘映像旁的弹出按钮
   - 或右键点击桌面上的磁盘映像图标，选择"弹出"

### 方式二：使用 ZIP 压缩包

1. **下载 ZIP 文件**
   - 下载 `LifeTimer-1.0.zip` 文件

2. **解压缩文件**
   - 双击 ZIP 文件进行解压
   - 或使用解压软件

3. **移动应用**
   - 将解压出的 `LifeTimer.app` 移动到 `/Applications` 文件夹
   - 可以通过 Finder 或拖拽完成

## 🔐 安全设置

### 首次运行

由于 LifeTimer 不是从 Mac App Store 下载的应用，macOS 可能会显示安全警告。

#### 情况一：显示"无法打开应用"

1. **打开系统偏好设置**
   - 点击苹果菜单 → 系统偏好设置
   - 选择"安全性与隐私"

2. **允许应用运行**
   - 在"通用"标签页中
   - 找到关于 LifeTimer 的提示
   - 点击"仍要打开"按钮

#### 情况二：使用右键菜单

1. **右键点击应用**
   - 在 Applications 文件夹中找到 LifeTimer.app
   - 右键点击应用图标

2. **选择打开**
   - 从上下文菜单中选择"打开"
   - 在弹出的对话框中点击"打开"

### Gatekeeper 设置

如果您信任开发者，可以临时调整 Gatekeeper 设置：

```bash
# 临时允许任何来源的应用（不推荐）
sudo spctl --master-disable

# 恢复默认设置
sudo spctl --master-enable
```

**注意**: 建议使用上述右键打开的方法，而不是完全禁用 Gatekeeper。

## ⚙️ 权限设置

LifeTimer 需要以下权限才能正常工作：

### 1. 文件访问权限
- **用途**: 选择音乐文件夹
- **设置**: 首次选择文件夹时会自动请求

### 2. 网络访问权限
- **用途**: 云同步功能（可选）
- **设置**: 自动获取，无需手动设置

### 3. 通知权限
- **用途**: 番茄钟完成提醒
- **设置**: 
  1. 系统偏好设置 → 通知与专注模式
  2. 找到 LifeTimer
  3. 启用"允许通知"

### 4. 菜单栏访问
- **用途**: 显示计时器状态
- **设置**: 自动获取，无需手动设置

## 🎵 音频设置

### 配置背景音乐

1. **准备音乐文件**
   - 支持格式: MP3, M4A, WAV, AAC, FLAC
   - 建议创建专门的音乐文件夹

2. **在应用中设置**
   - 打开 LifeTimer
   - 进入设置页面
   - 点击"选择音乐文件夹"
   - 选择包含音乐文件的文件夹

### 音频权限

如果音频播放有问题：

1. **检查系统音量**
   - 确保系统音量不为零
   - 检查应用内音量设置

2. **检查音频输出**
   - 系统偏好设置 → 声音
   - 确认输出设备正确

## 🔄 更新应用

### 检查更新

目前 LifeTimer 需要手动更新：

1. 访问官方网站或 GitHub 页面
2. 下载最新版本
3. 按照安装步骤重新安装

### 保留数据

更新时您的数据会自动保留，包括：
- 设置偏好
- 历史记录
- 自定义配置

## 🗑️ 卸载应用

如果需要卸载 LifeTimer：

### 删除应用

1. **删除主应用**
   - 在 Applications 文件夹中找到 LifeTimer.app
   - 将其拖拽到废纸篓

2. **清理用户数据**（可选）
   ```bash
   # 删除偏好设置文件
   rm ~/Library/Preferences/com.yourcompany.LifeTimer.plist
   
   # 删除应用支持文件
   rm -rf ~/Library/Application\ Support/LifeTimer
   ```

## ❓ 常见问题

### Q: 应用无法启动
**A**: 
1. 检查系统版本是否满足要求
2. 尝试重新下载和安装
3. 检查安全设置

### Q: 无法选择音乐文件夹
**A**: 
1. 确保文件夹包含支持的音频格式
2. 检查文件夹权限
3. 尝试选择其他文件夹

### Q: 通知不显示
**A**: 
1. 检查系统通知设置
2. 确认 LifeTimer 通知权限已启用
3. 检查"勿扰模式"设置

### Q: 菜单栏图标不显示
**A**: 
1. 重启应用
2. 检查菜单栏是否已满
3. 在设置中重新启用菜单栏功能

### Q: 云同步不工作
**A**: 
1. 检查网络连接
2. 验证服务器设置
3. 查看同步日志

## 📞 技术支持

如果您遇到其他问题：

### 联系方式
- **邮箱**: <EMAIL>
- **GitHub**: https://github.com/yourname/LifeTimer
- **文档**: 查看项目文档文件夹

### 报告问题时请提供
- macOS 版本
- LifeTimer 版本
- 详细的问题描述
- 错误截图（如有）

## 🎉 开始使用

安装完成后，您可以：

1. **启动应用**
   - 在 Applications 文件夹中双击 LifeTimer.app
   - 或使用 Spotlight 搜索"LifeTimer"

2. **基本设置**
   - 设置番茄钟时长
   - 选择背景音乐
   - 配置通知偏好

3. **开始专注**
   - 选择任务
   - 点击开始按钮
   - 享受专注时光！

---

**祝您使用愉快！🍅**
