<?php
// 测试统计接口的认证功能
require_once 'config/database.php';
require_once 'includes/auth.php';
require_once 'includes/functions.php';

// 创建测试会话token
function createTestSession() {
    $db = getDB();
    
    // 获取第一个用户
    $stmt = $db->query("SELECT id FROM users LIMIT 1");
    $user = $stmt->fetch();
    
    if (!$user) {
        echo "No users found in database\n";
        return null;
    }
    
    $userId = $user['id'];
    
    // 获取该用户的第一个设备
    $stmt = $db->prepare("SELECT id FROM devices WHERE user_id = ? LIMIT 1");
    $stmt->execute([$userId]);
    $device = $stmt->fetch();
    
    if (!$device) {
        echo "No devices found for user\n";
        return null;
    }
    
    $deviceId = $device['id'];
    
    // 创建新的会话token
    $token = generateSessionToken();
    $expiresAt = new DateTime();
    $expiresAt->add(new DateInterval("PT24H")); // 24小时后过期
    
    $stmt = $db->prepare('
        INSERT INTO user_sessions (user_id, device_id, session_token, expires_at, is_active)
        VALUES (?, ?, ?, ?, 1)
    ');
    
    $stmt->execute([
        $userId,
        $deviceId,
        $token,
        $expiresAt->format('Y-m-d H:i:s')
    ]);
    
    echo "Created test session token: $token\n";
    echo "User ID: $userId, Device ID: $deviceId\n";
    echo "Expires at: " . $expiresAt->format('Y-m-d H:i:s') . "\n";
    
    return $token;
}

// 测试统计接口
function testStatisticAPI($token, $endpoint) {
    $url = "http://localhost:8080/$endpoint";
    
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $token
            ]
        ]
    ]);

    echo "\n=== Testing $endpoint ===\n";
    echo "URL: $url\n";
    echo "Token: " . substr($token, 0, 16) . "...\n";
    
    $response = file_get_contents($url, false, $context);

    if ($response === false) {
        echo "API call failed\n";
        return;
    }

    $data = json_decode($response, true);
    echo "Response:\n";
    echo json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
}

// 运行测试
echo "=== 统计接口认证测试 ===\n\n";

echo "1. 创建测试会话token...\n";
$token = createTestSession();

if ($token) {
    echo "\n2. 测试周统计接口...\n";
    testStatisticAPI($token, 'get_week_statistic');
    
    echo "\n3. 测试日统计接口...\n";
    testStatisticAPI($token, 'get_day_statistic');
    
    echo "\n4. 测试日统计接口（指定日期）...\n";
    testStatisticAPI($token, 'get_day_statistic?date=2025-07-27');
} else {
    echo "Failed to create test session\n";
}

echo "\n=== 测试完成 ===\n";
?>
